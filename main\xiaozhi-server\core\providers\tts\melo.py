import os
import time
import uuid
import threading
from datetime import datetime
from config.logger import setup_logging
from core.providers.tts.base import TTSProviderBase

TAG = __name__
logger = setup_logging()

# 全局锁，用于确保模型初始化的线程安全
_model_lock = threading.Lock()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)

        # MeloTTS配置参数
        self.language = config.get("language", "ZH")  # 默认中文
        self.device = config.get("device", "auto")    # 设备选择：auto, cpu, cuda, mps

        # 处理说话人配置
        if config.get("private_voice"):
            self.speaker = config.get("private_voice")
        else:
            self.speaker = config.get("speaker", "ZH")  # 默认中文说话人

        # 处理速度参数
        speed = config.get("speed", "1.0")
        self.speed = float(speed) if speed else 1.0

        # 初始化MeloTTS模型
        self.model = None
        self.speaker_ids = None
        self._initialize_model()

    def _initialize_model(self):
        """初始化MeloTTS模型"""
        with _model_lock:
            try:
                from melo.api import TTS

                start_time = int(time.time() * 1000)
                logger.bind(tag=TAG).info(f"开始初始化MeloTTS模型，语言: {self.language}，设备: {self.device}")

                # 创建TTS模型实例
                self.model = TTS(language=self.language, device=self.device)
                self.speaker_ids = self.model.hps.data.spk2id

                end_time = int(time.time() * 1000)
                logger.bind(tag=TAG).info(f"MeloTTS模型初始化完成，耗时: {end_time - start_time}毫秒")
                logger.bind(tag=TAG).info(f"可用说话人: {list(self.speaker_ids.keys())}")

            except ImportError as e:
                error_msg = f"MeloTTS库未安装，请先安装: pip install -e . && python -m unidic download"
                logger.bind(tag=TAG).error(error_msg)
                raise Exception(error_msg)
            except Exception as e:
                error_msg = f"MeloTTS模型初始化失败: {e}"
                logger.bind(tag=TAG).error(error_msg)
                raise Exception(error_msg)

    def generate_filename(self, extension=".wav"):
        """生成音频文件名"""
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file):
        """异步文本转语音"""
        try:
            start_time = int(time.time() * 1000)
            logger.bind(tag=TAG).info(f"开始MeloTTS文本转语音，文本: {text[:50]}...")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            # 获取说话人ID
            if self.speaker in self.speaker_ids:
                speaker_id = self.speaker_ids[self.speaker]
            else:
                # 如果指定的说话人不存在，使用默认说话人
                available_speakers = list(self.speaker_ids.keys())
                if available_speakers:
                    default_speaker = available_speakers[0]
                    speaker_id = self.speaker_ids[default_speaker]
                    logger.bind(tag=TAG).warning(f"说话人 {self.speaker} 不存在，使用默认说话人: {default_speaker}")
                else:
                    raise Exception("没有可用的说话人")

            # 执行文本转语音
            self.model.tts_to_file(
                text=text,
                speaker_id=speaker_id,
                output_path=output_file,
                speed=self.speed
            )

            # 检查文件是否生成成功
            if not os.path.exists(output_file):
                raise Exception("音频文件生成失败")

            end_time = int(time.time() * 1000)
            logger.bind(tag=TAG).info(f"MeloTTS文本转语音完成，耗时: {end_time - start_time}毫秒")

        except Exception as e:
            error_msg = f"MeloTTS文本转语音失败: {e}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
