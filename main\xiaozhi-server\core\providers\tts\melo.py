import os
import time
import uuid
import requests
from datetime import datetime
from config.logger import setup_logging
from core.providers.tts.base import TTSProviderBase

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)

        # MeloTTS API配置
        self.api_url = config.get("api_url", "http://localhost:8000/tts")

        # MeloTTS配置参数
        self.language = config.get("language", "ZH")  # 默认中文

        # 处理说话人配置
        if config.get("private_voice"):
            self.speaker = config.get("private_voice")
        else:
            self.speaker = config.get("speaker", "ZH")  # 默认中文说话人

        # 处理速度参数
        speed = config.get("speed", "1.0")
        self.speed = float(speed) if speed else 1.0

        # 高级参数配置
        self.sdp_ratio = float(config.get("sdp_ratio", "0.2"))
        self.noise_scale = float(config.get("noise_scale", "0.6"))
        self.noise_scale_w = float(config.get("noise_scale_w", "0.8"))

        # 请求超时设置
        timeout = config.get("timeout", "30")
        self.timeout = int(timeout) if timeout else 30

        # 请求头配置
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "xiaozhi-server/1.0"
        }

        # 添加自定义请求头
        custom_headers = config.get("headers", {})
        if custom_headers:
            self.headers.update(custom_headers)

        logger.bind(tag=TAG).info(f"MeloTTS API初始化完成，API地址: {self.api_url}，语言: {self.language}，说话人: {self.speaker}")

    def generate_filename(self, extension=".wav"):
        """生成音频文件名"""
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file):
        """异步文本转语音"""
        try:
            start_time = int(time.time() * 1000)
            logger.bind(tag=TAG).info(f"开始MeloTTS API文本转语音，文本: {text[:50]}...")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            # 构建请求参数
            request_data = {
                "text": text,
                "language": self.language,
                "speaker": self.speaker,
                "speed": self.speed,
                "sdp_ratio": self.sdp_ratio,
                "noise_scale": self.noise_scale,
                "noise_scale_w": self.noise_scale_w
            }

            logger.bind(tag=TAG).debug(f"请求参数: {request_data}")

            # 发送POST请求
            response = requests.post(
                self.api_url,
                json=request_data,
                headers=self.headers,
                timeout=self.timeout
            )

            # 检查响应状态
            if response.status_code == 200:
                # 检查响应内容类型和内容
                content_type = response.headers.get('Content-Type', '')
                content_length = len(response.content)

                # 如果内容类型包含audio或者内容长度大于1000字节（可能是音频数据）
                if 'audio' in content_type or content_length > 1000:
                    # 保存音频文件
                    with open(output_file, "wb") as audio_file:
                        audio_file.write(response.content)

                    # 验证文件是否成功生成
                    if not os.path.exists(output_file) or os.path.getsize(output_file) == 0:
                        raise Exception("音频文件生成失败或文件为空")

                    end_time = int(time.time() * 1000)
                    logger.bind(tag=TAG).info(f"MeloTTS API文本转语音完成，耗时: {end_time - start_time}毫秒，文件大小: {content_length} 字节")
                else:
                    # 如果返回的可能是错误信息，尝试解析
                    try:
                        error_info = response.json()
                        error_msg = f"MeloTTS API返回错误信息: {error_info}"
                    except:
                        error_msg = f"MeloTTS API返回非音频内容: {response.text[:200]}"
                    raise Exception(error_msg)
            else:
                # 处理HTTP错误状态码
                try:
                    error_info = response.json()
                    error_msg = f"MeloTTS API请求失败: {response.status_code} - {error_info}"
                except:
                    error_msg = f"MeloTTS API请求失败: {response.status_code} - {response.text[:200]}"
                raise Exception(error_msg)

        except requests.exceptions.Timeout:
            error_msg = f"MeloTTS API请求超时 (超过{self.timeout}秒)"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = f"MeloTTS API连接失败，请检查API地址: {self.api_url}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"MeloTTS API请求异常: {e}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"MeloTTS API文本转语音失败: {e}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
