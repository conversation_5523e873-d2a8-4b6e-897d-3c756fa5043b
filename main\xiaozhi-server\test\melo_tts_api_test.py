#!/usr/bin/env python3
"""
MeloTTS API Provider 测试脚本
测试新实现的基于API的MeloTTS提供者是否正常工作
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.providers.tts.melo import TTSProvider
from config.logger import setup_logging

def test_melo_tts_api():
    """测试MeloTTS API提供者"""
    logger = setup_logging()
    
    # 测试配置
    config = {
        "output_dir": "tmp/",
        "api_url": "http://localhost:8000/tts",
        "language": "ZH",
        "speaker": "ZH",
        "speed": 1.0,
        "sdp_ratio": 0.2,
        "noise_scale": 0.6,
        "noise_scale_w": 0.8,
        "timeout": 30,
        "headers": {
            "User-Agent": "xiaozhi-server/1.0"
        }
    }
    
    # 测试文本
    test_text = "床前明月光，疑是地上霜。举头望明月，低头思故乡。"
    
    try:
        # 创建TTS提供者实例
        tts_provider = TTSProvider(config, delete_audio_file=False)
        logger.info(f"MeloTTS API提供者创建成功")
        
        # 生成文件名
        output_file = tts_provider.generate_filename()
        logger.info(f"生成输出文件名: {output_file}")
        
        # 执行文本转语音
        logger.info(f"开始转换文本: {test_text}")
        asyncio.run(tts_provider.text_to_speak(test_text, output_file))
        
        # 检查结果
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            logger.info(f"✅ 测试成功！音频文件已生成: {output_file}")
            logger.info(f"文件大小: {file_size} 字节")
            
            # 可选：播放音频（需要安装sounddevice和soundfile）
            try:
                import sounddevice as sd
                import soundfile as sf
                
                data, samplerate = sf.read(output_file)
                logger.info(f"音频信息 - 采样率: {samplerate}, 时长: {len(data)/samplerate:.2f}秒")
                
                # 询问是否播放
                play_audio = input("是否播放生成的音频？(y/n): ").lower().strip()
                if play_audio == 'y':
                    logger.info("正在播放音频...")
                    sd.play(data, samplerate)
                    sd.wait()
                    logger.info("音频播放完成")
                    
            except ImportError:
                logger.warning("未安装sounddevice或soundfile，跳过音频播放测试")
            except Exception as e:
                logger.error(f"音频播放失败: {e}")
                
        else:
            logger.error("❌ 测试失败！音频文件未生成")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败！错误: {e}")
        return False
    
    return True

def test_different_languages():
    """测试不同语言"""
    logger = setup_logging()
    
    # 测试不同语言和说话人
    test_configs = [
        {
            "language": "ZH",
            "speaker": "ZH",
            "text": "你好，我是语音助手。"
        },
        {
            "language": "EN",
            "speaker": "EN-US",
            "text": "Hello, I am a voice assistant."
        },
        {
            "language": "EN",
            "speaker": "EN-BR",
            "text": "Hello, I am a voice assistant with British accent."
        },
        {
            "language": "JP",
            "speaker": "JP",
            "text": "こんにちは、私は音声アシスタントです。"
        }
    ]
    
    for test_config in test_configs:
        logger.info(f"\n测试语言: {test_config['language']}, 说话人: {test_config['speaker']}")
        
        config = {
            "output_dir": "tmp/",
            "api_url": "http://localhost:8000/tts",
            "language": test_config["language"],
            "speaker": test_config["speaker"],
            "speed": 1.0,
            "sdp_ratio": 0.2,
            "noise_scale": 0.6,
            "noise_scale_w": 0.8,
            "timeout": 30
        }
        
        try:
            tts_provider = TTSProvider(config, delete_audio_file=False)
            output_file = tts_provider.generate_filename()
            
            asyncio.run(tts_provider.text_to_speak(test_config["text"], output_file))
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"✅ {test_config['language']}-{test_config['speaker']} 测试成功，文件大小: {file_size} 字节")
            else:
                logger.error(f"❌ {test_config['language']}-{test_config['speaker']} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_config['language']}-{test_config['speaker']} 测试失败: {e}")

def test_api_connectivity():
    """测试API连接性"""
    logger = setup_logging()
    
    import requests
    
    api_url = "http://localhost:8000"
    
    try:
        # 测试健康检查
        logger.info("测试API健康检查...")
        response = requests.get(f"{api_url}/", timeout=5)
        if response.status_code == 200:
            logger.info("✅ API服务正常运行")
            logger.info(f"服务信息: {response.json()}")
        else:
            logger.error(f"❌ API健康检查失败: {response.status_code}")
            return False
            
        # 测试获取支持的语言
        logger.info("测试获取支持的语言...")
        response = requests.get(f"{api_url}/languages", timeout=5)
        if response.status_code == 200:
            languages = response.json()
            logger.info(f"✅ 支持的语言: {languages}")
        else:
            logger.error(f"❌ 获取语言列表失败: {response.status_code}")
            
        # 测试获取说话人
        logger.info("测试获取英语说话人...")
        response = requests.get(f"{api_url}/speakers/EN", timeout=5)
        if response.status_code == 200:
            speakers = response.json()
            logger.info(f"✅ 英语说话人: {speakers}")
        else:
            logger.error(f"❌ 获取说话人列表失败: {response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        logger.error("❌ 无法连接到MeloTTS API服务，请确保服务已启动")
        logger.error("启动命令: python api_server.py --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        logger.error(f"❌ API连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("MeloTTS API Provider 测试")
    print("=" * 50)
    
    # API连接性测试
    print("\n1. API连接性测试")
    api_ok = test_api_connectivity()
    
    if api_ok:
        # 基础功能测试
        print("\n2. 基础功能测试")
        success = test_melo_tts_api()
        
        if success:
            # 不同语言测试
            print("\n3. 不同语言测试")
            test_different_languages()
    else:
        print("\n⚠️  API服务未运行，跳过功能测试")
        print("请先启动MeloTTS API服务:")
        print("python api_server.py --host 0.0.0.0 --port 8000")
    
    print("\n测试完成！")
