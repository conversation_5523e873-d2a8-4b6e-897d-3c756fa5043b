-- 多租户权限初始化脚本

-- ================================
-- 1. 初始化权限数据
-- ================================

-- 企业管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('ORG_MANAGE', '企业管理', 1, 0, '/organization', 'organization/index', 'el-icon-office-building', 10, 1, NOW()),
('ORG_VIEW', '企业查看', 3, 1, NULL, NULL, NULL, 11, 1, NOW()),
('ORG_CREATE', '企业创建', 2, 1, NULL, NULL, NULL, 12, 1, NOW()),
('ORG_UPDATE', '企业编辑', 2, 1, NULL, NULL, NULL, 13, 1, NOW()),
('ORG_DELETE', '企业删除', 2, 1, NULL, NULL, NULL, 14, 1, NOW());

-- 租户管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('TENANT_MANAGE', '租户管理', 1, 0, '/tenant', 'tenant/index', 'el-icon-user', 20, 1, NOW()),
('TENANT_VIEW', '租户查看', 3, 6, NULL, NULL, NULL, 21, 1, NOW()),
('TENANT_CREATE', '租户创建', 2, 6, NULL, NULL, NULL, 22, 1, NOW()),
('TENANT_UPDATE', '租户编辑', 2, 6, NULL, NULL, NULL, 23, 1, NOW()),
('TENANT_DELETE', '租户删除', 2, 6, NULL, NULL, NULL, 24, 1, NOW());

-- 用户管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('USER_MANAGE', '用户管理', 1, 0, '/user', 'user/index', 'el-icon-user', 30, 1, NOW()),
('USER_VIEW', '用户查看', 3, 11, NULL, NULL, NULL, 31, 1, NOW()),
('USER_CREATE', '用户创建', 2, 11, NULL, NULL, NULL, 32, 1, NOW()),
('USER_UPDATE', '用户编辑', 2, 11, NULL, NULL, NULL, 33, 1, NOW()),
('USER_DELETE', '用户删除', 2, 11, NULL, NULL, NULL, 34, 1, NOW());

-- 角色管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('ROLE_MANAGE', '角色管理', 1, 0, '/role', 'role/index', 'el-icon-s-custom', 40, 1, NOW()),
('ROLE_VIEW', '角色查看', 3, 16, NULL, NULL, NULL, 41, 1, NOW()),
('ROLE_CREATE', '角色创建', 2, 16, NULL, NULL, NULL, 42, 1, NOW()),
('ROLE_UPDATE', '角色编辑', 2, 16, NULL, NULL, NULL, 43, 1, NOW()),
('ROLE_DELETE', '角色删除', 2, 16, NULL, NULL, NULL, 44, 1, NOW());

-- 智能体管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('AGENT_MANAGE', '智能体管理', 1, 0, '/agent', 'agent/index', 'el-icon-cpu', 50, 1, NOW()),
('AGENT_VIEW', '智能体查看', 3, 21, NULL, NULL, NULL, 51, 1, NOW()),
('AGENT_CREATE', '智能体创建', 2, 21, NULL, NULL, NULL, 52, 1, NOW()),
('AGENT_UPDATE', '智能体编辑', 2, 21, NULL, NULL, NULL, 53, 1, NOW()),
('AGENT_DELETE', '智能体删除', 2, 21, NULL, NULL, NULL, 54, 1, NOW());

-- 设备管理模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('DEVICE_MANAGE', '设备管理', 1, 0, '/device', 'device/index', 'el-icon-mobile-phone', 60, 1, NOW()),
('DEVICE_VIEW', '设备查看', 3, 26, NULL, NULL, NULL, 61, 1, NOW()),
('DEVICE_BIND', '设备绑定', 2, 26, NULL, NULL, NULL, 62, 1, NOW()),
('DEVICE_UNBIND', '设备解绑', 2, 26, NULL, NULL, NULL, 63, 1, NOW()),
('DEVICE_RESET', '设备重置', 2, 26, NULL, NULL, NULL, 64, 1, NOW()),
('DEVICE_DISABLE', '设备禁用', 2, 26, NULL, NULL, NULL, 65, 1, NOW());

-- 报表统计模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('REPORT_MANAGE', '报表统计', 1, 0, '/report', 'report/index', 'el-icon-data-analysis', 70, 1, NOW()),
('REPORT_VIEW', '报表查看', 3, 31, NULL, NULL, NULL, 71, 1, NOW()),
('REPORT_EXPORT', '报表导出', 2, 31, NULL, NULL, NULL, 72, 1, NOW());

-- 系统配置模块权限
INSERT INTO `sys_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `path`, `component`, `icon`, `sort`, `creator`, `create_date`) VALUES
('SYSTEM_MANAGE', '系统配置', 1, 0, '/system', 'system/index', 'el-icon-setting', 80, 1, NOW()),
('SYSTEM_CONFIG', '系统配置', 2, 34, NULL, NULL, NULL, 81, 1, NOW()),
('SYSTEM_LOG', '系统日志', 3, 34, NULL, NULL, NULL, 82, 1, NOW());

-- ================================
-- 2. 初始化角色数据
-- ================================

-- 平台角色
INSERT INTO `sys_role` (`role_code`, `role_name`, `role_type`, `tenant_id`, `data_scope`, `creator`, `create_date`) VALUES
('PLATFORM_ADMIN', '平台管理员', 1, NULL, 1, 1, NOW()),
('PLATFORM_OPERATOR', '平台运营', 1, NULL, 1, 1, NOW());

-- 租户角色模板（用于创建租户时复制）
INSERT INTO `sys_role` (`role_code`, `role_name`, `role_type`, `tenant_id`, `data_scope`, `creator`, `create_date`) VALUES
('TENANT_ADMIN', '租户管理员', 2, 1, 2, 1, NOW()),
('TENANT_DEVICE_MANAGER', '设备管理员', 2, 1, 2, 1, NOW()),
('TENANT_OPERATOR', '租户运营', 2, 1, 2, 1, NOW()),
('TENANT_USER', '普通用户', 2, 1, 4, 1, NOW());

-- ================================
-- 3. 初始化角色权限关联
-- ================================

-- 平台管理员拥有所有权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`)
SELECT 1, `id`, 1, NOW() FROM `sys_permission`;

-- 平台运营权限（企业管理、租户管理、用户管理、报表统计）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`) VALUES
(2, 1, 1, NOW()), (2, 2, 1, NOW()), (2, 3, 1, NOW()), (2, 4, 1, NOW()),
(2, 6, 1, NOW()), (2, 7, 1, NOW()), (2, 8, 1, NOW()), (2, 9, 1, NOW()),
(2, 11, 1, NOW()), (2, 12, 1, NOW()), (2, 13, 1, NOW()), (2, 14, 1, NOW()),
(2, 31, 1, NOW()), (2, 32, 1, NOW()), (2, 33, 1, NOW());

-- 租户管理员权限（用户管理、角色管理、智能体查看、设备管理、报表统计）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`) VALUES
(3, 11, 1, NOW()), (3, 12, 1, NOW()), (3, 13, 1, NOW()), (3, 14, 1, NOW()), (3, 15, 1, NOW()),
(3, 16, 1, NOW()), (3, 17, 1, NOW()), (3, 18, 1, NOW()), (3, 19, 1, NOW()), (3, 20, 1, NOW()),
(3, 21, 1, NOW()), (3, 22, 1, NOW()),
(3, 26, 1, NOW()), (3, 27, 1, NOW()), (3, 28, 1, NOW()), (3, 29, 1, NOW()), (3, 30, 1, NOW()), (3, 31, 1, NOW()),
(3, 32, 1, NOW()), (3, 33, 1, NOW()), (3, 34, 1, NOW());

-- 设备管理员权限（智能体查看、设备管理）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`) VALUES
(4, 21, 1, NOW()), (4, 22, 1, NOW()),
(4, 26, 1, NOW()), (4, 27, 1, NOW()), (4, 28, 1, NOW()), (4, 29, 1, NOW()), (4, 30, 1, NOW()), (4, 31, 1, NOW());

-- 租户运营权限（智能体查看、设备查看、报表统计）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`) VALUES
(5, 21, 1, NOW()), (5, 22, 1, NOW()),
(5, 26, 1, NOW()), (5, 27, 1, NOW()),
(5, 32, 1, NOW()), (5, 33, 1, NOW()), (5, 34, 1, NOW());

-- 普通用户权限（智能体查看、设备查看）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `creator`, `create_date`) VALUES
(6, 21, 1, NOW()), (6, 22, 1, NOW()),
(6, 26, 1, NOW()), (6, 27, 1, NOW());

-- ================================
-- 4. 初始化用户角色关联
-- ================================

-- 将现有用户设置为平台管理员
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `creator`, `create_date`) VALUES (1, 1, 1, NOW());

-- ================================
-- 5. 更新租户管理员关联
-- ================================

-- 更新租户表中的管理员用户ID
UPDATE `sys_tenant` SET `admin_user_id` = 1 WHERE `id` = 1;
