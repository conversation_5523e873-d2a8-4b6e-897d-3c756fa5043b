#!/usr/bin/env python3
"""
Index TTS Provider 测试脚本
测试新实现的Index TTS提供者是否正常工作
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.providers.tts.index import TTSProvider
from config.logger import setup_logging

def test_index_tts():
    """测试Index TTS提供者"""
    logger = setup_logging()
    
    # 测试配置
    config = {
        "output_dir": "tmp/",
        "api_url": "http://8.155.62.189:7861/tts",
        "character": "小云",
        "timeout": 30,
        "headers": {
            "User-Agent": "xiaozhi-server/1.0"
        }
    }
    
    # 测试文本
    test_text = "床前明月光，疑是地上霜。举头望明月，低头思故乡。"
    
    try:
        # 创建TTS提供者实例
        tts_provider = TTSProvider(config, delete_audio_file=False)
        logger.info(f"Index TTS提供者创建成功")
        
        # 生成文件名
        output_file = tts_provider.generate_filename()
        logger.info(f"生成输出文件名: {output_file}")
        
        # 执行文本转语音
        logger.info(f"开始转换文本: {test_text}")
        asyncio.run(tts_provider.text_to_speak(test_text, output_file))
        
        # 检查结果
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            logger.info(f"✅ 测试成功！音频文件已生成: {output_file}")
            logger.info(f"文件大小: {file_size} 字节")
            
            # 可选：播放音频（需要安装sounddevice和soundfile）
            try:
                import sounddevice as sd
                import soundfile as sf
                
                data, samplerate = sf.read(output_file)
                logger.info(f"音频信息 - 采样率: {samplerate}, 时长: {len(data)/samplerate:.2f}秒")
                
                # 询问是否播放
                play_audio = input("是否播放生成的音频？(y/n): ").lower().strip()
                if play_audio == 'y':
                    logger.info("正在播放音频...")
                    sd.play(data, samplerate)
                    sd.wait()
                    logger.info("音频播放完成")
                    
            except ImportError:
                logger.warning("未安装sounddevice或soundfile，跳过音频播放测试")
            except Exception as e:
                logger.error(f"音频播放失败: {e}")
                
        else:
            logger.error("❌ 测试失败！音频文件未生成")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败！错误: {e}")
        return False
    
    return True

def test_different_characters():
    """测试不同角色"""
    logger = setup_logging()
    
    characters = ["小云", "小雨", "小雪"]  # 可根据实际支持的角色调整
    test_text = "你好，我是语音助手。"
    
    for character in characters:
        logger.info(f"\n测试角色: {character}")
        
        config = {
            "output_dir": "tmp/",
            "api_url": "http://8.155.62.189:7861/tts",
            "character": character,
            "timeout": 30
        }
        
        try:
            tts_provider = TTSProvider(config, delete_audio_file=False)
            output_file = tts_provider.generate_filename()
            
            asyncio.run(tts_provider.text_to_speak(test_text, output_file))
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.info(f"✅ 角色 {character} 测试成功，文件大小: {file_size} 字节")
            else:
                logger.error(f"❌ 角色 {character} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ 角色 {character} 测试失败: {e}")

if __name__ == "__main__":
    print("Index TTS Provider 测试")
    print("=" * 50)
    
    # 基础功能测试
    print("\n1. 基础功能测试")
    success = test_index_tts()
    
    if success:
        # 不同角色测试
        print("\n2. 不同角色测试")
        test_different_characters()
    
    print("\n测试完成！")
