# 设备记忆存储功能测试用例

## 1. 单元测试用例

### 1.1 DeviceMemoryService 测试

#### 1.1.1 保存设备记忆测试
```java
@Test
public void testSaveDeviceMemory_NewDevice_Success() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceEntity device = createTestDevice(macAddress);
    device.setSummaryMemory(null); // 模拟新设备，没有记忆
    DeviceMemoryDTO dto = createTestMemoryDTO();

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.of(device));
    when(deviceRepository.updateMemory(any(), any()))
        .thenReturn(1);
    when(deviceRepository.findById(device.getId()))
        .thenReturn(Optional.of(createUpdatedDevice(device, dto)));

    // When
    DeviceMemoryResponseDTO result = deviceMemoryService.saveDeviceMemory(macAddress, dto);

    // Then
    assertNotNull(result);
    assertEquals(device.getId(), result.getDeviceId());
    assertEquals(dto.getSummaryMemory(), result.getSummaryMemory());
    verify(deviceRepository).updateMemory(
        eq(device.getId()),
        eq(dto.getSummaryMemory())
    );
}

@Test
public void testSaveDeviceMemory_ExistingDevice_Success() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceEntity device = createTestDevice(macAddress);
    device.setSummaryMemory("现有记忆内容"); // 模拟已有记忆的设备
    DeviceMemoryDTO dto = createTestMemoryDTO();

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.of(device));
    when(deviceRepository.updateMemory(any(), any()))
        .thenReturn(1);
    when(deviceRepository.findById(device.getId()))
        .thenReturn(Optional.of(createUpdatedDevice(device, dto)));

    // When
    DeviceMemoryResponseDTO result = deviceMemoryService.saveDeviceMemory(macAddress, dto);

    // Then
    assertNotNull(result);
    verify(deviceRepository).updateMemory(
        eq(device.getId()),
        eq(dto.getSummaryMemory())
    );
}

@Test
public void testSaveDeviceMemory_DeviceNotFound_ThrowsException() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceMemoryDTO dto = createTestMemoryDTO();

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.empty());

    // When & Then
    assertThrows(BusinessException.class, () -> {
        deviceMemoryService.saveDeviceMemory(macAddress, dto);
    });
}

@Test
public void testSaveDeviceMemory_UpdateFailed_ThrowsException() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceEntity device = createTestDevice(macAddress);
    device.setSummaryMemory("现有记忆内容");
    DeviceMemoryDTO dto = createTestMemoryDTO();

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.of(device));
    when(deviceRepository.updateMemory(any(), any()))
        .thenReturn(0); // 模拟更新失败（设备可能已被删除）

    // When & Then
    assertThrows(BusinessException.class, () -> {
        deviceMemoryService.saveDeviceMemory(macAddress, dto);
    });
}
```

#### 1.1.2 获取设备记忆测试
```java
@Test
public void testGetDeviceMemory_ExistingMemory_Success() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceEntity device = createTestDevice(macAddress);
    device.setSummaryMemory("测试记忆内容");

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.of(device));

    // When
    DeviceMemoryResponseDTO result = deviceMemoryService.getDeviceMemory(macAddress);

    // Then
    assertNotNull(result);
    assertEquals(device.getId(), result.getDeviceId());
    assertEquals(device.getSummaryMemory(), result.getSummaryMemory());
}

@Test
public void testGetDeviceMemory_NoMemory_ReturnsEmpty() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceEntity device = createTestDevice(macAddress);
    device.setSummaryMemory(null); // 没有记忆内容

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.of(device));

    // When
    DeviceMemoryResponseDTO result = deviceMemoryService.getDeviceMemory(macAddress);

    // Then
    assertNotNull(result);
    assertEquals(device.getId(), result.getDeviceId());
    assertNull(result.getSummaryMemory());
}

@Test
public void testGetDeviceMemory_DeviceNotFound_ThrowsException() {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";

    when(deviceRepository.findByMacAddressAndDeleted(macAddress, 0))
        .thenReturn(Optional.empty());

    // When & Then
    assertThrows(BusinessException.class, () -> {
        deviceMemoryService.getDeviceMemory(macAddress);
    });
}
```

### 1.2 DeviceMemoryController 测试

#### 1.2.1 保存设备记忆API测试
```java
@Test
public void testSaveDeviceMemory_ValidRequest_Success() throws Exception {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceMemoryDTO dto = createTestMemoryDTO();
    DeviceMemoryResponseDTO responseDTO = createTestMemoryResponseDTO();

    when(deviceMemoryService.saveDeviceMemory(macAddress, any(DeviceMemoryDTO.class)))
        .thenReturn(responseDTO);

    // When & Then
    mockMvc.perform(put("/api/agent/saveDeviceMemory/{macAddress}", macAddress)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(dto)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.deviceId").value(responseDTO.getDeviceId()))
            .andExpect(jsonPath("$.data.summaryMemory").value(responseDTO.getSummaryMemory()));
}

@Test
public void testSaveDeviceMemory_InvalidRequest_BadRequest() throws Exception {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceMemoryDTO dto = new DeviceMemoryDTO(); // 空的DTO，验证失败

    // When & Then
    mockMvc.perform(put("/api/agent/saveDeviceMemory/{macAddress}", macAddress)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(dto)))
            .andExpect(status().isBadRequest());
}

@Test
public void testSaveDeviceMemory_ServiceException_ErrorResponse() throws Exception {
    // Given
    String macAddress = "AA:BB:CC:DD:EE:FF";
    DeviceMemoryDTO dto = createTestMemoryDTO();

    when(deviceMemoryService.saveDeviceMemory(macAddress, any(DeviceMemoryDTO.class)))
        .thenThrow(new BusinessException("设备不存在"));

    // When & Then
    mockMvc.perform(put("/api/agent/saveDeviceMemory/{macAddress}", macAddress)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(dto)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(500))
            .andExpect(jsonPath("$.message").value("设备不存在"));
}
```

## 2. 集成测试用例

### 2.1 数据库集成测试

#### 2.1.1 设备记忆CRUD操作测试
```java
@Test
@Transactional
public void testDeviceMemoryCRUD() {
    // Create - 创建设备
    DeviceEntity device = new DeviceEntity();
    device.setMacAddress("AA:BB:CC:DD:EE:FF");
    device.setAgentId(1L);
    device.setSummaryMemory("测试记忆内容");
    device.setUpdateDate(LocalDateTime.now());

    DeviceEntity saved = deviceRepository.save(device);
    assertNotNull(saved.getId());

    // Read - 读取设备记忆
    Optional<DeviceEntity> found = deviceRepository
        .findByMacAddressAndDeleted("AA:BB:CC:DD:EE:FF", 0);
    assertTrue(found.isPresent());
    assertEquals("测试记忆内容", found.get().getSummaryMemory());

    // Update - 更新记忆
    int updated = deviceRepository.updateMemory(
        saved.getId(), "更新后的记忆");
    assertEquals(1, updated);

    // Verify update - 验证更新
    Optional<DeviceEntity> updatedDevice = deviceRepository
        .findById(saved.getId());
    assertTrue(updatedDevice.isPresent());
    assertEquals("更新后的记忆", updatedDevice.get().getSummaryMemory());
}
```

### 2.2 API集成测试

#### 2.2.1 完整流程测试
```java
@Test
@Transactional
public void testCompleteDeviceMemoryFlow() throws Exception {
    // 1. 保存设备记忆
    DeviceMemoryDTO saveDto = createTestMemoryDTO();

    MvcResult saveResult = mockMvc.perform(
        put("/api/agent/saveDeviceMemory/{macAddress}", "AA:BB:CC:DD:EE:FF")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(saveDto)))
        .andExpect(status().isOk())
        .andReturn();

    // 2. 获取设备记忆
    mockMvc.perform(get("/api/agent/getDeviceMemory/{macAddress}", "AA:BB:CC:DD:EE:FF"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data.summaryMemory").value(saveDto.getSummaryMemory()));

    // 3. 更新设备记忆
    DeviceMemoryDTO updateDto = createTestMemoryDTO();
    updateDto.setSummaryMemory("更新后的记忆内容");

    mockMvc.perform(
        put("/api/agent/saveDeviceMemory/{macAddress}", "AA:BB:CC:DD:EE:FF")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(updateDto)))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data.summaryMemory").value("更新后的记忆内容"));
}
```

## 3. 性能测试用例

### 3.1 并发更新测试
```java
@Test
public void testConcurrentMemoryUpdate() throws InterruptedException {
    int threadCount = 10;
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicInteger failureCount = new AtomicInteger(0);

    for (int i = 0; i < threadCount; i++) {
        final int threadId = i;
        new Thread(() -> {
            try {
                DeviceMemoryDTO dto = createTestMemoryDTO();
                dto.setSummaryMemory("线程" + threadId + "的记忆内容");

                deviceMemoryService.saveDeviceMemory("AA:BB:CC:DD:EE:FF", dto);
                successCount.incrementAndGet();
            } catch (Exception e) {
                failureCount.incrementAndGet();
            } finally {
                latch.countDown();
            }
        }).start();
    }

    latch.await(30, TimeUnit.SECONDS);

    // 验证结果
    assertTrue(successCount.get() > 0);
    // 由于并发冲突，可能会有失败的情况
    System.out.println("成功: " + successCount.get() + ", 失败: " + failureCount.get());
}
```

## 4. 兼容性测试用例

### 4.1 旧API兼容性测试
```java
@Test
public void testLegacyApiCompatibility() throws Exception {
    // 使用旧的API格式
    AgentMemoryDTO legacyDto = new AgentMemoryDTO();
    legacyDto.setSummaryMemory("通过旧API保存的记忆");

    // 调用旧API
    mockMvc.perform(put("/api/agent/saveMemory/{macAddress}", "AA:BB:CC:DD:EE:FF")
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(legacyDto)))
            .andExpect(status().isOk())
            .andExpect(header().string("X-Deprecated-API", "true"));

    // 验证数据是否正确保存
    mockMvc.perform(get("/api/agent/getDeviceMemory/{macAddress}", "AA:BB:CC:DD:EE:FF"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.summaryMemory").value("通过旧API保存的记忆"));
}
```

## 5. 数据迁移测试用例

### 5.1 数据迁移验证测试
```java
@Test
@Sql(scripts = "/test-data/agent-memory-data.sql")
public void testDataMigration() {
    // 执行数据迁移脚本
    migrationService.migrateAgentMemoryToDeviceMemory();

    // 验证迁移结果
    List<DeviceMemoryEntity> migratedMemories = deviceMemoryRepository.findAll();
    assertFalse(migratedMemories.isEmpty());

    // 验证数据完整性
    for (DeviceMemoryEntity memory : migratedMemories) {
        assertNotNull(memory.getDeviceId());
        assertNotNull(memory.getSummaryMemory());
    }
}
```

## 6. 测试数据工厂

### 6.1 测试数据创建方法
```java
public class TestDataFactory {

    public static DeviceEntity createTestDevice(String macAddress) {
        DeviceEntity device = new DeviceEntity();
        device.setId(1L);
        device.setMacAddress(macAddress);
        device.setAgentId(1L);

        return device;
    }

    public static DeviceMemoryDTO createTestMemoryDTO() {
        DeviceMemoryDTO dto = new DeviceMemoryDTO();
        dto.setSummaryMemory("测试记忆内容");
        return dto;
    }

    public static DeviceEntity createUpdatedDevice(DeviceEntity originalDevice, DeviceMemoryDTO dto) {
        DeviceEntity device = new DeviceEntity();
        device.setId(originalDevice.getId());
        device.setMacAddress(originalDevice.getMacAddress());
        device.setAgentId(originalDevice.getAgentId());
        device.setSummaryMemory(dto.getSummaryMemory());
        device.setUpdateDate(new Date());
        return device;
    }

    public static DeviceMemoryResponseDTO createTestMemoryResponseDTO() {
        DeviceMemoryResponseDTO dto = new DeviceMemoryResponseDTO();
        dto.setDeviceId(1L);
        dto.setSummaryMemory("测试记忆内容");
        dto.setUpdatedTime(new Date());
        return dto;
    }
}
```

这些测试用例覆盖了设备记忆功能的各个方面，包括正常流程、异常情况、兼容性和数据迁移等，确保功能的可靠性和稳定性。由于移除了并发控制机制，测试更加简洁，专注于核心业务逻辑的验证。
