# MeloTTS API Server

A FastAPI-based REST API server for MeloTTS (Text-to-Speech) that provides HTTP endpoints for generating speech from text.

## Features

- **Multi-language support**: EN, ES, FR, ZH, JP, KR
- **Multiple speakers**: Especially for English (EN-Default, EN-US, EN-BR, EN_INDIA, EN-AU)
- **Speed control**: Adjustable speech speed from 0.1 to 10.0
- **Advanced parameters**: Control over SDP ratio, noise scales
- **RESTful API**: Easy integration with any application
- **Async support**: High-performance async FastAPI framework
- **Auto documentation**: Built-in Swagger UI and ReDoc

## Installation

Make sure you have MeloTTS installed and working. The API server uses the same dependencies as the main MeloTTS project.

```bash
# Install additional dependencies for the API server
pip install fastapi uvicorn pydantic
```

## Usage

### Starting the Server

```bash
# Basic usage
python api_server.py

# Custom host and port
python api_server.py --host 0.0.0.0 --port 8000

# Specify device (auto, cpu, cuda, mps)
python api_server.py --device cuda

# Multiple workers (for production)
python api_server.py --workers 4

# All options
python api_server.py --host 0.0.0.0 --port 8000 --device auto --workers 1 --log-level info
```

### Command Line Arguments

- `--host`: Host to bind to (default: 0.0.0.0)
- `--port`: Port to bind to (default: 8000)
- `--device`: Device to use - auto, cpu, cuda, mps (default: auto)
- `--workers`: Number of worker processes (default: 1)
- `--log-level`: Log level - debug, info, warning, error (default: info)

## API Endpoints

### Health Check
```
GET /
```
Returns server status and loaded models.

### Get Supported Languages
```
GET /languages
```
Returns list of supported languages.

### Get Speakers for Language
```
GET /speakers/{language}
```
Returns available speakers for a specific language.

### Text-to-Speech (Main Endpoint)
```
POST /tts
```

**Request Body:**
```json
{
    "text": "Hello, this is a test.",
    "language": "EN",
    "speaker": "EN-US",
    "speed": 1.0,
    "sdp_ratio": 0.2,
    "noise_scale": 0.6,
    "noise_scale_w": 0.8
}
```

**Parameters:**
- `text` (required): Text to synthesize
- `language` (optional): Language code (default: "EN")
- `speaker` (optional): Speaker ID (uses default if not specified)
- `speed` (optional): Speech speed 0.1-10.0 (default: 1.0)
- `sdp_ratio` (optional): SDP ratio (default: 0.2)
- `noise_scale` (optional): Noise scale (default: 0.6)
- `noise_scale_w` (optional): Noise scale w (default: 0.8)

**Response:** WAV audio file (audio/wav)

### Simple TTS Endpoint
```
POST /tts_simple
```

Compatible with index-tts-vllm API format. Accepts the same parameters as `/tts` but with a simpler interface.

## Supported Languages and Speakers

### English (EN)
- EN-Default
- EN-US (American)
- EN-BR (British)
- EN_INDIA (Indian)
- EN-AU (Australian)

### Other Languages
- **Spanish (ES)**: ES
- **French (FR)**: FR
- **Chinese (ZH)**: ZH
- **Japanese (JP)**: JP
- **Korean (KR)**: KR

## Example Usage

### Python Client Example

```python
import requests

# Basic TTS request
response = requests.post(
    "http://localhost:8000/tts",
    json={
        "text": "Hello, this is a test of the MeloTTS API.",
        "language": "EN",
        "speaker": "EN-US",
        "speed": 1.0
    }
)

# Save audio file
if response.status_code == 200:
    with open("output.wav", "wb") as f:
        f.write(response.content)
    print("Audio saved to output.wav")
else:
    print(f"Error: {response.status_code}")
    print(response.json())
```

### cURL Example

```bash
# Get supported languages
curl http://localhost:8000/languages

# Get speakers for English
curl http://localhost:8000/speakers/EN

# Generate TTS
curl -X POST "http://localhost:8000/tts" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Hello world!",
       "language": "EN",
       "speaker": "EN-US",
       "speed": 1.0
     }' \
     --output output.wav
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');
const fs = require('fs');

async function generateTTS() {
    try {
        const response = await axios.post('http://localhost:8000/tts', {
            text: 'Hello, this is a test.',
            language: 'EN',
            speaker: 'EN-US',
            speed: 1.0
        }, {
            responseType: 'arraybuffer'
        });
        
        fs.writeFileSync('output.wav', response.data);
        console.log('Audio saved to output.wav');
    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
    }
}

generateTTS();
```

## Testing

Use the provided test script to verify the API server:

```bash
# Basic test
python test_api_server.py

# Test specific language and speaker
python test_api_server.py --language EN --speaker EN-US --text "Hello world"

# Test all languages and speakers
python test_api_server.py --test-all

# Custom server URL
python test_api_server.py --url http://your-server:8000
```

## API Documentation

Once the server is running, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## Error Handling

The API returns appropriate HTTP status codes:

- **200**: Success
- **400**: Bad request (invalid parameters)
- **404**: Not found (unsupported language)
- **500**: Internal server error

Error responses include detailed error messages:

```json
{
    "status": "error",
    "error": "Error description",
    "traceback": "Detailed traceback (in debug mode)"
}
```

## Performance Tips

1. **Use GPU**: Set `--device cuda` for faster inference
2. **Multiple workers**: Use `--workers 4` for production deployments
3. **Keep models loaded**: The server loads all models at startup for faster response times
4. **Batch requests**: For multiple TTS requests, consider batching them

## Troubleshooting

1. **Models not loading**: Ensure MeloTTS models are properly downloaded
2. **CUDA errors**: Check GPU availability and CUDA installation
3. **Memory issues**: Reduce number of workers or use CPU device
4. **Port conflicts**: Change port with `--port` argument

## License

This API server follows the same license as the MeloTTS project.
