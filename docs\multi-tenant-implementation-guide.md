# 多租户改造实施指南

## 1. 前置准备

### 1.1 环境要求
- Java 8+
- MySQL 5.7+
- Redis 3.0+
- Maven 3.6+

### 1.2 备份现有数据
```bash
# 备份数据库
mysqldump -u root -p xiaozhi_db > xiaozhi_backup_$(date +%Y%m%d_%H%M%S).sql

# 备份代码
git tag v1.0-before-multitenant
git push origin v1.0-before-multitenant
```

## 2. 数据库改造

### 2.1 执行数据库脚本
```bash
# 1. 执行基础表结构创建和现有表改造
mysql -u root -p xiaozhi_db < docs/multi-tenant-init.sql

# 2. 执行权限数据初始化
mysql -u root -p xiaozhi_db < docs/multi-tenant-permissions.sql
```

### 2.2 验证数据库改造
```sql
-- 检查新增表是否创建成功
SHOW TABLES LIKE 'sys_%';

-- 检查现有表是否添加了tenant_id字段
DESCRIBE sys_user;
DESCRIBE ai_agent;
DESCRIBE ai_device;

-- 检查基础数据是否初始化成功
SELECT * FROM sys_organization;
SELECT * FROM sys_tenant;
SELECT * FROM sys_role;
SELECT * FROM sys_permission LIMIT 10;
```

## 3. 后端代码改造

### 3.1 创建实体类

#### 3.1.1 企业组织实体
```java
// main/manager-api/src/main/java/xiaozhi/modules/sys/entity/OrganizationEntity.java
@Data
@TableName("sys_organization")
@Schema(description = "企业组织")
public class OrganizationEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "企业编码")
    private String orgCode;
    
    @Schema(description = "企业名称")
    private String orgName;
    
    @Schema(description = "企业类型")
    private Integer orgType;
    
    @Schema(description = "联系人")
    private String contactPerson;
    
    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Schema(description = "联系邮箱")
    private String contactEmail;
    
    @Schema(description = "企业地址")
    private String address;
    
    @Schema(description = "状态")
    private Integer status;
    
    @Schema(description = "到期时间")
    private Date expireDate;
    
    @Schema(description = "备注")
    private String remark;
}
```

#### 3.1.2 租户实体
```java
// main/manager-api/src/main/java/xiaozhi/modules/sys/entity/TenantEntity.java
@Data
@TableName("sys_tenant")
@Schema(description = "租户")
public class TenantEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "租户编码")
    private String tenantCode;
    
    @Schema(description = "租户名称")
    private String tenantName;
    
    @Schema(description = "关联企业ID")
    private Long orgId;
    
    @Schema(description = "租户管理员用户ID")
    private Long adminUserId;
    
    @Schema(description = "最大用户数")
    private Integer maxUsers;
    
    @Schema(description = "最大设备数")
    private Integer maxDevices;
    
    @Schema(description = "最大智能体数")
    private Integer maxAgents;
    
    @Schema(description = "状态")
    private Integer status;
    
    @Schema(description = "到期时间")
    private Date expireDate;
    
    @Schema(description = "备注")
    private String remark;
}
```

### 3.2 创建租户过滤拦截器

#### 3.2.1 租户上下文
```java
// main/manager-api/src/main/java/xiaozhi/common/tenant/TenantContext.java
public class TenantContext {
    private static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();
    
    public static void setTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }
    
    public static Long getTenantId() {
        return TENANT_ID.get();
    }
    
    public static void clear() {
        TENANT_ID.remove();
    }
}
```

#### 3.2.2 租户拦截器
```java
// main/manager-api/src/main/java/xiaozhi/common/tenant/TenantInterceptor.java
@Component
public class TenantInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取当前用户的租户ID
        UserDetail user = SecurityUser.getUser();
        if (user != null && user.getTenantId() != null) {
            TenantContext.setTenantId(user.getTenantId());
        }
        
        try {
            return invocation.proceed();
        } finally {
            TenantContext.clear();
        }
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
}
```

### 3.3 修改现有实体类

#### 3.3.1 用户实体修改
```java
// 在 UserEntity 中添加字段
@Schema(description = "租户ID")
private Long tenantId;

@Schema(description = "用户类型")
private Integer userType;
```

#### 3.3.2 智能体实体修改
```java
// 在 AgentEntity 中添加字段
@Schema(description = "租户ID")
private Long tenantId;
```

#### 3.3.3 设备实体修改
```java
// 在 DeviceEntity 中添加字段
@Schema(description = "租户ID")
private Long tenantId;
```

### 3.4 创建权限注解

#### 3.4.1 数据权限注解
```java
// main/manager-api/src/main/java/xiaozhi/common/annotation/DataScope.java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {
    /**
     * 租户字段名
     */
    String value() default "tenant_id";
    
    /**
     * 是否启用租户过滤
     */
    boolean enabled() default true;
}
```

#### 3.4.2 租户过滤注解
```java
// main/manager-api/src/main/java/xiaozhi/common/annotation/TenantFilter.java
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TenantFilter {
    /**
     * 是否启用租户过滤
     */
    boolean enabled() default true;
}
```

## 4. 前端改造

### 4.1 添加租户管理页面

#### 4.1.1 企业管理页面
```vue
<!-- main/manager-web/src/views/system/Organization.vue -->
<template>
  <div class="organization-management">
    <el-card>
      <div slot="header">
        <span>企业管理</span>
        <el-button style="float: right;" type="primary" @click="handleAdd">新增企业</el-button>
      </div>
      
      <el-table :data="organizationList" v-loading="loading">
        <el-table-column prop="orgCode" label="企业编码"></el-table-column>
        <el-table-column prop="orgName" label="企业名称"></el-table-column>
        <el-table-column prop="contactPerson" label="联系人"></el-table-column>
        <el-table-column prop="contactPhone" label="联系电话"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
```

#### 4.1.2 租户管理页面
```vue
<!-- main/manager-web/src/views/system/Tenant.vue -->
<template>
  <div class="tenant-management">
    <el-card>
      <div slot="header">
        <span>租户管理</span>
        <el-button style="float: right;" type="primary" @click="handleAdd">新增租户</el-button>
      </div>
      
      <el-table :data="tenantList" v-loading="loading">
        <el-table-column prop="tenantCode" label="租户编码"></el-table-column>
        <el-table-column prop="tenantName" label="租户名称"></el-table-column>
        <el-table-column prop="orgName" label="所属企业"></el-table-column>
        <el-table-column prop="maxUsers" label="最大用户数"></el-table-column>
        <el-table-column prop="maxDevices" label="最大设备数"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
```

### 4.2 修改路由配置

```javascript
// main/manager-web/src/router/index.js
{
  path: '/system',
  component: Layout,
  meta: { title: '系统管理', icon: 'el-icon-setting' },
  children: [
    {
      path: 'organization',
      component: () => import('@/views/system/Organization'),
      meta: { title: '企业管理', permission: 'ORG_VIEW' }
    },
    {
      path: 'tenant',
      component: () => import('@/views/system/Tenant'),
      meta: { title: '租户管理', permission: 'TENANT_VIEW' }
    }
  ]
}
```

## 5. 测试验证

### 5.1 功能测试清单

#### 5.1.1 企业管理测试
- [ ] 企业创建功能
- [ ] 企业编辑功能
- [ ] 企业删除功能
- [ ] 企业状态切换

#### 5.1.2 租户管理测试
- [ ] 租户创建功能
- [ ] 租户编辑功能
- [ ] 租户删除功能
- [ ] 租户配额限制

#### 5.1.3 权限控制测试
- [ ] 平台管理员权限
- [ ] 租户管理员权限
- [ ] 普通用户权限
- [ ] 数据隔离验证

#### 5.1.4 数据隔离测试
- [ ] 用户数据隔离
- [ ] 智能体数据隔离
- [ ] 设备数据隔离
- [ ] 跨租户访问拦截

### 5.2 性能测试

#### 5.2.1 数据库性能
```sql
-- 测试租户过滤查询性能
EXPLAIN SELECT * FROM ai_device WHERE tenant_id = 1;
EXPLAIN SELECT * FROM ai_agent WHERE tenant_id = 1;
EXPLAIN SELECT * FROM sys_user WHERE tenant_id = 1;
```

#### 5.2.2 并发测试
- 多租户并发访问测试
- 大数据量查询性能测试
- 内存使用情况监控

## 6. 部署上线

### 6.1 生产环境部署

#### 6.1.1 数据库迁移
```bash
# 1. 备份生产数据库
mysqldump -u root -p xiaozhi_prod > xiaozhi_prod_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行迁移脚本
mysql -u root -p xiaozhi_prod < docs/multi-tenant-init.sql
mysql -u root -p xiaozhi_prod < docs/multi-tenant-permissions.sql

# 3. 验证迁移结果
mysql -u root -p xiaozhi_prod -e "SELECT COUNT(*) FROM sys_organization;"
mysql -u root -p xiaozhi_prod -e "SELECT COUNT(*) FROM sys_tenant;"
```

#### 6.1.2 应用部署
```bash
# 1. 构建应用
mvn clean package -Dmaven.test.skip=true

# 2. 部署后端
cp target/xiaozhi-manager-api.jar /opt/xiaozhi/
systemctl restart xiaozhi-manager-api

# 3. 部署前端
npm run build
cp -r dist/* /var/www/xiaozhi-web/
```

### 6.2 监控配置

#### 6.2.1 数据库监控
- 租户数据量监控
- 查询性能监控
- 连接数监控

#### 6.2.2 应用监控
- 内存使用监控
- 响应时间监控
- 错误日志监控

## 7. 运维指南

### 7.1 日常维护

#### 7.1.1 租户管理
- 定期检查租户配额使用情况
- 监控租户到期时间
- 清理过期租户数据

#### 7.1.2 性能优化
- 定期分析慢查询
- 优化数据库索引
- 清理历史数据

### 7.2 故障处理

#### 7.2.1 常见问题
- 租户数据访问异常
- 权限验证失败
- 数据隔离失效

#### 7.2.2 应急处理
- 数据恢复流程
- 权限重置流程
- 系统回滚流程

## 8. 总结

多租户改造完成后，系统将具备：
- 完善的租户隔离机制
- 灵活的权限控制体系
- 可扩展的企业管理功能
- 安全的数据访问控制

为企业级SaaS服务提供了坚实的技术基础。
