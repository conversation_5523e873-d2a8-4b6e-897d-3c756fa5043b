import os
import time
import uuid
import requests
from datetime import datetime
from config.logger import setup_logging
from core.providers.tts.base import TTSProviderBase

TAG = __name__
logger = setup_logging()

class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)

        # Index TTS API配置
        self.api_url = config.get("api_url", "http://8.155.62.189:7861/tts")

        # 处理语音角色配置
        if config.get("private_voice"):
            self.character = config.get("private_voice")
        else:
            self.character = config.get("character", "小云")  # 默认角色

        # 请求超时设置
        timeout = config.get("timeout", "30")
        self.timeout = int(timeout) if timeout else 30

        # 请求头配置
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "xiaozhi-server/1.0"
        }

        # 添加自定义请求头
        custom_headers = config.get("headers", {})
        if custom_headers:
            self.headers.update(custom_headers)

        logger.bind(tag=TAG).info(f"Index TTS初始化完成，API地址: {self.api_url}，角色: {self.character}")

    def generate_filename(self, extension=".wav"):
        """生成音频文件名"""
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file):
        """异步文本转语音"""
        try:
            start_time = int(time.time() * 1000)
            logger.bind(tag=TAG).info(f"开始Index TTS文本转语音，文本: {text[:50]}...")

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)

            # 构建请求参数
            request_data = {
                "text": text,
                "character": self.character
            }

            logger.bind(tag=TAG).debug(f"请求参数: {request_data}")

            # 发送POST请求
            response = requests.post(
                self.api_url,
                json=request_data,
                headers=self.headers,
                timeout=self.timeout
            )

            # 检查响应状态
            if response.status_code == 200:
                # 检查响应内容类型和内容
                content_type = response.headers.get('Content-Type', '')
                content_length = len(response.content)

                # 如果内容类型包含audio或者内容长度大于1000字节（可能是音频数据）
                if 'audio' in content_type or content_length > 1000:
                    # 保存音频文件
                    with open(output_file, "wb") as audio_file:
                        audio_file.write(response.content)

                    # 验证文件是否成功生成
                    if not os.path.exists(output_file) or os.path.getsize(output_file) == 0:
                        raise Exception("音频文件生成失败或文件为空")

                    end_time = int(time.time() * 1000)
                    logger.bind(tag=TAG).info(f"Index TTS文本转语音完成，耗时: {end_time - start_time}毫秒，文件大小: {content_length} 字节")
                else:
                    # 如果返回的可能是错误信息，尝试解析
                    try:
                        error_info = response.json()
                        error_msg = f"Index TTS返回错误信息: {error_info}"
                    except:
                        error_msg = f"Index TTS返回非音频内容: {response.text[:200]}"
                    raise Exception(error_msg)
            else:
                # 处理HTTP错误状态码
                try:
                    error_info = response.json()
                    error_msg = f"Index TTS请求失败: {response.status_code} - {error_info}"
                except:
                    error_msg = f"Index TTS请求失败: {response.status_code} - {response.text[:200]}"
                raise Exception(error_msg)

        except requests.exceptions.Timeout:
            error_msg = f"Index TTS请求超时 (超过{self.timeout}秒)"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = f"Index TTS连接失败，请检查API地址: {self.api_url}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"Index TTS请求异常: {e}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Index TTS文本转语音失败: {e}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
