package xiaozhi.modules.device.dao;

import java.util.Date;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.device.entity.DeviceEntity;

@Mapper
public interface DeviceDao extends BaseMapper<DeviceEntity> {
    /**
     * 获取此智能体全部设备的最后连接时间
     *
     * @param agentId 智能体id
     * @return date
     */
    Date getAllLastConnectedAtByAgentId(String agentId);

    /**
     * 更新设备记忆
     *
     * @param deviceId 设备ID
     * @param summaryMemory 记忆内容
     * @return 更新行数
     */
    @Update("UPDATE ai_device SET summary_memory = #{summaryMemory}, update_date = NOW() WHERE id = #{deviceId}")
    int updateMemory(@Param("deviceId") String deviceId, @Param("summaryMemory") String summaryMemory);

}