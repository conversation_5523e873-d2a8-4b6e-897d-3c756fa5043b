# 设备记忆存储功能重新设计文档

## 1. 问题分析

### 1.1 当前问题
- **记忆覆盖问题**：一个Agent下的多个设备共享同一个`summary_memory`字段，导致设备间记忆相互覆盖
- **设计不合理**：记忆应该是设备级别的，而不是Agent级别的
- **数据丢失风险**：多设备并发更新时可能导致记忆数据丢失

### 1.2 业务需求
- 每个设备应该有独立的记忆存储空间
- 设备记忆不应相互影响
- 支持设备记忆的增量更新和完整替换
- 保持API的向后兼容性

## 2. 解决方案设计

### 2.1 设计方案对比

#### 方案一：独立的device_memory表
**优点：** 职责分离、支持版本历史、便于扩展
**缺点：** 增加复杂度、需要JOIN操作、占用更多存储

#### 方案二：在device表中直接添加字段（推荐）
**优点：** 简化结构、提高性能、减少存储开销、符合1:1关系
**缺点：** device表字段增多、扩展性稍弱

### 2.2 最终方案
考虑到Device和DeviceMemory是1:1关系，且记忆数据相对简单，**采用方案二：在device表中直接添加记忆字段**。

### 2.3 整体架构
```
Agent (1) -----> (N) Device (包含记忆字段)
```

### 2.4 设计原则
1. **设备独立性**：每个设备拥有独立的记忆存储
2. **数据一致性**：确保记忆数据的完整性和一致性
3. **向后兼容**：保持现有API的兼容性
4. **性能优化**：减少JOIN操作，提高查询性能

## 3. 数据库设计

### 3.1 修改device表结构

```sql
-- 在device表中添加记忆相关字段
ALTER TABLE `device`
ADD COLUMN `summary_memory` text COMMENT '总结记忆内容',
ADD COLUMN `conversation_memory` text COMMENT '对话记忆内容（预留）',
ADD COLUMN `context_memory` text COMMENT '上下文记忆内容（预留）',
ADD COLUMN `memory_version` int NOT NULL DEFAULT '1' COMMENT '记忆版本号，用于并发控制',
ADD COLUMN `memory_updated_time` datetime DEFAULT NULL COMMENT '记忆最后更新时间';

-- 添加记忆相关索引
CREATE INDEX `idx_device_memory_updated` ON `device` (`memory_updated_time`);
CREATE INDEX `idx_device_memory_version` ON `device` (`memory_version`);
```

### 3.2 数据迁移脚本

```sql
-- 数据迁移：将agent表中的summary_memory迁移到device表
UPDATE device d
INNER JOIN agent a ON d.agent_id = a.id
SET
    d.summary_memory = a.summary_memory,
    d.memory_version = 1,
    d.memory_updated_time = NOW()
WHERE a.summary_memory IS NOT NULL AND a.summary_memory != '';

-- 清理agent表中的summary_memory字段（可选，建议保留一段时间后再删除）
-- ALTER TABLE agent DROP COLUMN summary_memory;
```

## 4. API设计

### 4.1 保存设备记忆 API

#### 4.1.1 接口定义
```
PUT /api/agent/saveDeviceMemory/{macAddress}
```

#### 4.1.2 请求参数
```json
{
    "summaryMemory": "设备的总结记忆内容",
    "conversationMemory": "对话记忆内容（可选）",
    "contextMemory": "上下文记忆内容（可选）",
    "memoryVersion": 1
}
```

#### 4.1.3 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "deviceId": 123,
        "memoryVersion": 2,
        "updatedTime": "2024-01-01T12:00:00"
    }
}
```

### 4.2 获取设备记忆 API

#### 4.2.1 接口定义
```
GET /api/agent/getDeviceMemory/{macAddress}
```

#### 4.2.2 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "deviceId": 123,
        "summaryMemory": "设备的总结记忆内容",
        "conversationMemory": "对话记忆内容",
        "contextMemory": "上下文记忆内容",
        "memoryVersion": 2,
        "updatedTime": "2024-01-01T12:00:00"
    }
}
```

### 4.3 兼容性API（保持原有接口）

#### 4.3.1 接口定义
```
PUT /api/agent/saveMemory/{macAddress}
```

#### 4.3.2 说明
- 保持原有接口不变，内部逻辑改为调用新的设备记忆存储
- 添加@Deprecated注解，建议使用新接口
- 在响应中添加警告信息，提示使用新接口

## 5. 代码实现

### 5.1 实体类设计

#### 5.1.1 修改DeviceEntity
```java
@Entity
@Table(name = "device")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "mac_address", nullable = false)
    private String macAddress;

    @Column(name = "agent_id", nullable = false)
    private Long agentId;

    // 原有字段...

    // 新增记忆相关字段
    @Column(name = "summary_memory", columnDefinition = "TEXT")
    private String summaryMemory;

    @Column(name = "conversation_memory", columnDefinition = "TEXT")
    private String conversationMemory;

    @Column(name = "context_memory", columnDefinition = "TEXT")
    private String contextMemory;

    @Column(name = "memory_version", nullable = false)
    private Integer memoryVersion = 1;

    @Column(name = "memory_updated_time")
    private LocalDateTime memoryUpdatedTime;
}
```

#### 5.1.2 DTO类设计
```java
// 设备记忆DTO
@Data
public class DeviceMemoryDTO {
    @NotBlank(message = "总结记忆不能为空")
    private String summaryMemory;

    private String conversationMemory;

    private String contextMemory;

    @Min(value = 1, message = "记忆版本号必须大于0")
    private Integer memoryVersion;
}

// 设备记忆响应DTO
@Data
public class DeviceMemoryResponseDTO {
    private Long deviceId;
    private String summaryMemory;
    private String conversationMemory;
    private String contextMemory;
    private Integer memoryVersion;
    private LocalDateTime updatedTime;
}
```

### 5.2 Repository层
```java
@Repository
public interface DeviceRepository extends JpaRepository<DeviceEntity, Long> {

    Optional<DeviceEntity> findByMacAddressAndDeleted(String macAddress, Integer deleted);

    @Modifying
    @Query("UPDATE DeviceEntity d SET d.summaryMemory = :summaryMemory, " +
           "d.conversationMemory = :conversationMemory, d.contextMemory = :contextMemory, " +
           "d.memoryVersion = d.memoryVersion + 1, d.memoryUpdatedTime = CURRENT_TIMESTAMP " +
           "WHERE d.id = :deviceId AND d.memoryVersion = :currentVersion AND d.deleted = 0")
    int updateMemoryWithVersion(@Param("deviceId") Long deviceId,
                               @Param("summaryMemory") String summaryMemory,
                               @Param("conversationMemory") String conversationMemory,
                               @Param("contextMemory") String contextMemory,
                               @Param("currentVersion") Integer currentVersion);

    @Modifying
    @Query("UPDATE DeviceEntity d SET d.summaryMemory = :summaryMemory, " +
           "d.conversationMemory = :conversationMemory, d.contextMemory = :contextMemory, " +
           "d.memoryVersion = 1, d.memoryUpdatedTime = CURRENT_TIMESTAMP " +
           "WHERE d.id = :deviceId AND d.deleted = 0")
    int initializeMemory(@Param("deviceId") Long deviceId,
                        @Param("summaryMemory") String summaryMemory,
                        @Param("conversationMemory") String conversationMemory,
                        @Param("contextMemory") String contextMemory);
}
```

### 5.3 Service层
```java
@Service
@Transactional
public class DeviceMemoryService {

    @Autowired
    private DeviceRepository deviceRepository;

    /**
     * 保存或更新设备记忆
     */
    public DeviceMemoryResponseDTO saveDeviceMemory(String macAddress, DeviceMemoryDTO dto) {
        DeviceEntity device = deviceRepository.findByMacAddressAndDeleted(macAddress, 0)
            .orElseThrow(() -> new BusinessException("设备不存在"));

        if (device.getSummaryMemory() == null) {
            // 初始化设备记忆
            return initializeDeviceMemory(device, dto);
        } else {
            // 更新现有记忆
            return updateDeviceMemory(device, dto);
        }
    }

    /**
     * 获取设备记忆
     */
    public DeviceMemoryResponseDTO getDeviceMemory(String macAddress) {
        DeviceEntity device = deviceRepository.findByMacAddressAndDeleted(macAddress, 0)
            .orElseThrow(() -> new BusinessException("设备不存在"));

        return convertToResponseDTO(device);
    }

    private DeviceMemoryResponseDTO updateDeviceMemory(DeviceEntity device, DeviceMemoryDTO dto) {
        int updatedRows = deviceRepository.updateMemoryWithVersion(
            device.getId(),
            dto.getSummaryMemory(),
            dto.getConversationMemory(),
            dto.getContextMemory(),
            dto.getMemoryVersion()
        );

        if (updatedRows == 0) {
            throw new BusinessException("记忆更新失败，可能存在并发冲突，请重试");
        }

        // 重新查询更新后的设备信息
        DeviceEntity updatedDevice = deviceRepository.findById(device.getId())
            .orElseThrow(() -> new BusinessException("设备不存在"));

        return convertToResponseDTO(updatedDevice);
    }

    private DeviceMemoryResponseDTO initializeDeviceMemory(DeviceEntity device, DeviceMemoryDTO dto) {
        int updatedRows = deviceRepository.initializeMemory(
            device.getId(),
            dto.getSummaryMemory(),
            dto.getConversationMemory(),
            dto.getContextMemory()
        );

        if (updatedRows == 0) {
            throw new BusinessException("记忆初始化失败");
        }

        // 重新查询初始化后的设备信息
        DeviceEntity updatedDevice = deviceRepository.findById(device.getId())
            .orElseThrow(() -> new BusinessException("设备不存在"));

        return convertToResponseDTO(updatedDevice);
    }

    private DeviceMemoryResponseDTO convertToResponseDTO(DeviceEntity device) {
        DeviceMemoryResponseDTO dto = new DeviceMemoryResponseDTO();
        dto.setDeviceId(device.getId());
        dto.setSummaryMemory(device.getSummaryMemory());
        dto.setConversationMemory(device.getConversationMemory());
        dto.setContextMemory(device.getContextMemory());
        dto.setMemoryVersion(device.getMemoryVersion());
        dto.setUpdatedTime(device.getMemoryUpdatedTime());
        return dto;
    }
}
```

### 5.4 Controller层

#### 5.4.1 新的设备记忆Controller
```java
@RestController
@RequestMapping("/api/agent")
@Api(tags = "设备记忆管理")
@Slf4j
public class DeviceMemoryController {

    @Autowired
    private DeviceMemoryService deviceMemoryService;

    @PutMapping("/saveDeviceMemory/{macAddress}")
    @Operation(summary = "保存设备记忆")
    public Result<DeviceMemoryResponseDTO> saveDeviceMemory(
            @PathVariable String macAddress,
            @RequestBody @Valid DeviceMemoryDTO dto) {
        try {
            DeviceMemoryResponseDTO response = deviceMemoryService.saveDeviceMemory(macAddress, dto);
            return Result.success(response);
        } catch (BusinessException e) {
            log.error("保存设备记忆失败: macAddress={}, error={}", macAddress, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("保存设备记忆异常: macAddress={}", macAddress, e);
            return Result.error("保存设备记忆失败");
        }
    }

    @GetMapping("/getDeviceMemory/{macAddress}")
    @Operation(summary = "获取设备记忆")
    public Result<DeviceMemoryResponseDTO> getDeviceMemory(@PathVariable String macAddress) {
        try {
            DeviceMemoryResponseDTO response = deviceMemoryService.getDeviceMemory(macAddress);
            return Result.success(response);
        } catch (BusinessException e) {
            log.error("获取设备记忆失败: macAddress={}, error={}", macAddress, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取设备记忆异常: macAddress={}", macAddress, e);
            return Result.error("获取设备记忆失败");
        }
    }
}
```

#### 5.4.2 修改现有AgentController（兼容性）
```java
@RestController
@RequestMapping("/api/agent")
public class AgentController {

    @Autowired
    private DeviceMemoryService deviceMemoryService;

    @PutMapping("/saveMemory/{macAddress}")
    @Operation(summary = "根据设备id更新智能体记忆（已废弃，请使用saveDeviceMemory）")
    @Deprecated
    public Result<Void> updateByDeviceId(
            @PathVariable String macAddress,
            @RequestBody @Valid AgentMemoryDTO dto) {
        try {
            // 转换为新的DTO格式
            DeviceMemoryDTO deviceMemoryDTO = new DeviceMemoryDTO();
            deviceMemoryDTO.setSummaryMemory(dto.getSummaryMemory());
            deviceMemoryDTO.setMemoryVersion(1); // 兼容性处理

            deviceMemoryService.saveDeviceMemory(macAddress, deviceMemoryDTO);

            // 在响应头中添加废弃警告
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder
                .currentRequestAttributes()).getResponse();
            if (response != null) {
                response.setHeader("X-Deprecated-API", "true");
                response.setHeader("X-Deprecated-Message", "请使用 /api/agent/saveDeviceMemory/{macAddress} 接口");
            }

            return Result.success();
        } catch (Exception e) {
            log.error("保存设备记忆失败: macAddress={}", macAddress, e);
            return Result.error("保存记忆失败");
        }
    }
}
```

## 6. 实施计划

### 6.1 第一阶段：数据库准备
1. **修改表结构**：在device表中添加记忆相关字段
2. **数据迁移**：将现有agent.summary_memory数据迁移到device表
3. **验证数据**：确认数据迁移的完整性和正确性

### 6.2 第二阶段：代码实现
1. **实体类修改**：在DeviceEntity中添加记忆字段和相关DTO
2. **Repository层**：在DeviceRepository中添加记忆操作方法
3. **Service层**：实现DeviceMemoryService
4. **Controller层**：实现新的API接口

### 6.3 第三阶段：兼容性处理
1. **修改现有接口**：更新AgentController中的saveMemory方法
2. **添加废弃标记**：为旧接口添加@Deprecated注解
3. **文档更新**：更新API文档，标明接口变更

### 6.4 第四阶段：测试验证
1. **单元测试**：编写完整的单元测试用例
2. **集成测试**：验证新旧接口的兼容性
3. **性能测试**：确认新方案的性能表现
4. **回归测试**：确保现有功能不受影响

## 7. 风险评估与应对

### 7.1 数据迁移风险
- **风险**：数据迁移过程中可能出现数据丢失或不一致
- **应对**：
  - 在迁移前进行完整的数据备份
  - 使用事务确保数据一致性
  - 迁移后进行数据校验

### 7.2 兼容性风险
- **风险**：现有客户端可能无法适应新的接口变更
- **应对**：
  - 保持旧接口的完全兼容性
  - 提供充分的过渡期
  - 详细的迁移指南

### 7.3 并发冲突风险
- **风险**：多设备同时更新记忆时可能出现并发冲突
- **应对**：
  - 使用乐观锁机制（版本号控制）
  - 提供重试机制
  - 详细的错误提示

## 8. 监控与运维

### 8.1 关键指标监控
- 设备记忆更新成功率
- 并发冲突发生频率
- API响应时间
- 数据库连接池使用情况

### 8.2 日志记录
- 记忆更新操作日志
- 并发冲突日志
- 数据迁移日志
- 性能监控日志

### 8.3 告警机制
- 记忆更新失败率超过阈值时告警
- 数据库连接异常告警
- API响应时间超过阈值告警

## 9. 后续优化方向

### 9.1 功能扩展
- 支持记忆的分类管理（对话记忆、上下文记忆等）
- 实现记忆的版本历史管理
- 支持记忆的导入导出功能

### 9.2 性能优化
- 实现记忆数据的缓存机制
- 优化数据库查询性能
- 支持记忆数据的分片存储

### 9.3 安全增强
- 实现记忆数据的加密存储
- 添加记忆访问权限控制
- 支持记忆数据的审计日志

## 10. 总结

本设计方案通过引入独立的设备记忆表，彻底解决了多设备记忆覆盖的问题，同时保持了良好的向后兼容性。方案具有以下优势：

1. **问题解决**：彻底解决记忆覆盖问题
2. **架构清晰**：设备记忆独立存储，逻辑清晰
3. **扩展性强**：支持未来功能扩展
4. **兼容性好**：保持现有API的兼容性
5. **可靠性高**：通过版本控制解决并发问题

通过分阶段实施，可以确保系统的平稳升级，最小化对现有业务的影响。
